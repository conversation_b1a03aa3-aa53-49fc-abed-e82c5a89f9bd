﻿# -*- coding: utf-8 -*-
import os
import subprocess
import sys
import json
import tempfile
import glob
from datetime import datetime
from pypinyin import pinyin, Style

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def get_input_mode_choice():
    """让用户选择输入模式：手动输入或batch_configs读取"""
    print("请选择输入模式：")
    print("1. 手动输入")
    print("2. batch_configs读取")

    while True:
        choice = input("请输入选择（1或2）：").strip()
        if choice in ['1', '2']:
            return choice
        else:
            print("输入无效，请输入1或2")

def apply_default_values(config):
    """为配置项应用默认值（题型字段除外）"""
    # 定义默认值
    defaults = {
        "处理模式": 1,
        "round2图片": 1,
        "模型ID": 1,
        "图像文件夹": 1,
        "像素增强": "n",
        "像素粘连": "n",
        "图像放大倍数": 1
    }

    # 应用默认值，但不覆盖已存在的值
    for key, default_value in defaults.items():
        if key not in config:
            config[key] = default_value
            print(f"  应用默认值: {key} = {default_value}")

    # 检查题型字段是否存在
    if "题型" not in config:
        print(f"  警告：配置中缺少必需的'题型'字段")
        return False

    return True

def get_latest_batch_config():
    """获取batch_configs文件夹中时间最晚的JSON文件"""
    batch_configs_dir = "batch_configs"
    if not os.path.exists(batch_configs_dir):
        print(f"错误：{batch_configs_dir} 文件夹不存在")
        return None

    # 查找所有JSON文件
    json_files = glob.glob(os.path.join(batch_configs_dir, "*.json"))
    if not json_files:
        print(f"错误：{batch_configs_dir} 文件夹中没有找到JSON文件")
        return None

    # 按文件名排序（假设文件名包含时间戳）
    json_files.sort(reverse=True)
    latest_file = json_files[0]

    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        print(f"已加载配置文件：{latest_file}")

        # 处理batch_configs中的每个配置项，应用默认值
        batch_configs = config_data.get("batch_configs", [])
        valid_configs = []

        for i, config in enumerate(batch_configs):
            print(f"\n处理第 {i+1} 个配置:")
            if apply_default_values(config):
                valid_configs.append(config)
                print(f"  ✓ 配置 {i+1} 验证通过")
            else:
                print(f"  ✗ 配置 {i+1} 验证失败，跳过此配置")

        # 更新配置数据
        config_data["batch_configs"] = valid_configs
        print(f"\n有效配置数量: {len(valid_configs)}/{len(batch_configs)}")

        return config_data
    except Exception as e:
        print(f"错误：无法读取配置文件 {latest_file}: {e}")
        return None

def get_stage_choice():
    """让用户选择单阶段还是双阶段"""
    print("请选择处理模式：")
    print("1. 单阶段")
    print("2. 双阶段")
    
    while True:
        choice = input("请输入选择（1或2）：").strip()
        if choice in ['1', '2']:
            return choice
        else:
            print("输入无效，请输入1或2")

def get_round2_image_choice():
    """让用户选择round2是否发图片"""
    print("round2是否发图片？")
    print("1. 是")
    print("2. 否")
    
    while True:
        choice = input("请输入选择（1或2）：").strip()
        if choice in ['1', '2']:
            return choice
        else:
            print("输入无效，请输入1或2")

def run_script(script_name, args=None):
    """运行指定的Python脚本"""
    try:
        print(f"\n正在运行 {script_name}...")
        print("=" * 50)

        # 构建命令
        cmd = [sys.executable, script_name]
        if args:
            cmd.extend(args)

        # 不捕获输出，让子进程直接与用户交互
        result = subprocess.run(cmd)

        print("=" * 50)
        if result.returncode == 0:
            print(f"✓ {script_name} 运行成功")
            return True
        else:
            print(f"✗ {script_name} 运行失败，返回码: {result.returncode}")
            return False

    except Exception as e:
        print(f"✗ 运行 {script_name} 时发生异常: {str(e)}")
        return False

def save_config(config_data):
    """保存配置到临时文件"""
    config_file = "temp_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, ensure_ascii=False, indent=2)
    return config_file

def load_config():
    """从临时文件加载配置"""
    config_file = "temp_config.json"
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return None

def cleanup_config():
    """清理临时配置文件"""
    config_file = "temp_config.json"
    if os.path.exists(config_file):
        try:
            os.remove(config_file)
        except:
            pass

def print_batch_summary(batch_results):
    """打印批处理执行总结"""
    print(f"\n{'='*80}")
    print("批处理执行总结")
    print(f"{'='*80}")

    total_batches = len(batch_results)
    successful_batches = [r for r in batch_results if r['success']]
    failed_batches = [r for r in batch_results if not r['success']]

    print(f"总批处理数量: {total_batches}")
    print(f"成功执行: {len(successful_batches)}")
    print(f"执行失败: {len(failed_batches)}")
    print(f"成功率: {len(successful_batches)/total_batches*100:.1f}%")

    if successful_batches:
        print(f"\n{'='*60}")
        print("✓ 成功执行的批处理:")
        print(f"{'='*60}")
        for result in successful_batches:
            config = result['config']
            print(f"\n第 {result['batch_index']} 个批处理:")
            print(f"  模式: {result['stage_mode']}")
            if result['stage_mode'] == '双阶段':
                print(f"  Round2: {result['round2_mode']}")
            print(f"  模型ID: {config.get('模型ID')}")
            print(f"  题型: {config.get('题型')}")
            print(f"  图像文件夹: {config.get('图像文件夹')}")
            print(f"  像素增强: {config.get('像素增强')}")
            print(f"  像素粘连: {config.get('像素粘连', 2)}")
            print(f"  放大倍数: {config.get('图像放大倍数')}")

            if result['output_files']:
                print(f"  生成的文档:")
                for output_file in result['output_files']:
                    print(f"    - {output_file['description']}: {output_file['directory']}")
                    # 尝试找到最新的md文件
                    try:
                        if os.path.exists(output_file['directory']):
                            md_files = [f for f in os.listdir(output_file['directory'])
                                      if f.endswith('.md') and not f.startswith('answer')]
                            if md_files:
                                md_files.sort(reverse=True)  # 按时间倒序
                                latest_md = md_files[0]
                                full_path = os.path.join(output_file['directory'], latest_md)
                                print(f"      最新文件: {full_path}")
                    except Exception as e:
                        print(f"      (无法检查文件: {e})")

    if failed_batches:
        print(f"\n{'='*60}")
        print("✗ 执行失败的批处理:")
        print(f"{'='*60}")
        for result in failed_batches:
            config = result['config']
            print(f"\n第 {result['batch_index']} 个批处理:")
            print(f"  模式: {result['stage_mode']}")
            if result['stage_mode'] == '双阶段':
                print(f"  Round2: {result['round2_mode']}")
            print(f"  模型ID: {config.get('模型ID')}")
            print(f"  题型: {config.get('题型')}")
            print(f"  图像文件夹: {config.get('图像文件夹')}")
            print(f"  像素增强: {config.get('像素增强')}")
            print(f"  像素粘连: {config.get('像素粘连', 2)}")
            print(f"  错误信息: {result.get('error_message', '未知错误')}")

    print(f"\n{'='*80}")
    print("批处理执行完成！")
    print(f"{'='*80}")

def run_single_batch_config(config, batch_index):
    """运行单个批处理配置，返回执行结果"""
    print(f"\n{'='*60}")
    print(f"开始执行第 {batch_index + 1} 个批处理配置")
    print(f"{'='*60}")

    # 从配置中提取参数（现在应该都有默认值了）
    stage_choice = str(config.get("处理模式", 1))
    round2_choice = str(config.get("round2图片", 1))
    model_id_num = config.get("模型ID", 1)
    question_type_num = config.get("题型")  # 题型是必需字段，不设默认值
    images_dir_num = config.get("图像文件夹", 1)
    use_enhance = config.get("像素增强", "n") == "y"  # 默认值改为"n"
    use_pixel_connection = config.get("像素粘连", "n") == "y"  # "y"=采用，"n"=不采用，默认为"n"
    scale = config.get("图像放大倍数", 1)  # 默认值改为1

    # 检查必需字段
    if question_type_num is None:
        print(f"错误：配置中缺少必需的'题型'字段")
        result = {
            'batch_index': batch_index + 1,
            'config': config,
            'success': False,
            'stage_mode': '单阶段' if stage_choice == '1' else '双阶段',
            'round2_mode': '发图片' if round2_choice == '1' else '不发图片',
            'output_files': [],
            'error_message': "配置中缺少必需的'题型'字段"
        }
        return result

    print(f"配置参数：")
    print(f"  处理模式: {stage_choice}")
    print(f"  round2图片: {round2_choice}")
    print(f"  模型ID: {model_id_num}")
    print(f"  题型: {question_type_num}")
    print(f"  图像文件夹: {images_dir_num}")
    print(f"  像素增强: {'是' if use_enhance else '否'}")
    print(f"  像素粘连: {'是' if use_pixel_connection else '否'}")
    print(f"  图像放大倍数: {scale}")

    # 初始化结果对象
    result = {
        'batch_index': batch_index + 1,
        'config': config,
        'success': False,
        'stage_mode': '单阶段' if stage_choice == '1' else '双阶段',
        'round2_mode': '发图片' if round2_choice == '1' else '不发图片',
        'output_files': [],
        'error_message': None
    }

    # 映射模型ID - 转换为模型字符串
    model_mapping = {
        1: "doubao-seed-1-6-250615",
        2: "doubao-seed-1-6-flash-250715",
        3: "doubao-1-5-thinking-vision-pro-250428",
        4: "doubao-1-5-vision-pro-32k-250115"
    }
    model_id = model_mapping.get(model_id_num, "doubao-seed-1-6-flash-250715")

    # 映射题型 - 转换为题型字符串和拼音
    question_types = {
        1: "涂卡选择题",
        2: "涂卡判断题",
        3: "连线题",
        4: "图表题",
        5: "翻译题",
        6: "画图题",
        7: "数学应用题",
        8: "数学计算题",
        9: "简单的四则运算",
        10: "填空题",
        11: "判断题",
        12: "多选题",
        13: "单选题"
    }
    question_type = question_types.get(question_type_num, "涂卡选择题")
    pinyin_name = chinese_to_pinyin(question_type)

    # 映射图像文件夹 - 转换为文件夹名称
    folder_mapping = {
        1: "images",
        2: "OpenCV_result",
        3: "grounding_result",
        4: "YOLO_result",
        5: "YOLO_text_result",
        6: "manual_result",
        7: "roboflow_yolo_result"
    }
    images_dir_name = folder_mapping.get(images_dir_num, "images")

    if stage_choice == '1':
        # 单阶段模式
        print(f"\n执行单阶段模式")

        # 导入one_stage_test模块并调用其函数
        try:
            import one_stage_test
            success = one_stage_test.run_one_stage_test(
                model_id=model_id,
                question_type=question_type,
                pinyin_name=pinyin_name,
                images_dir=images_dir_name,
                use_enhance=use_enhance,
                scale=scale,
                use_pixel_connection=use_pixel_connection
            )
            if success:
                print(f"✓ 第 {batch_index + 1} 个配置（单阶段）处理完成！")
                result['success'] = True
                # 添加输出文件路径
                types_dir = "types"
                question_dir = os.path.join(types_dir, pinyin_name)
                one_stage_response_dir = os.path.join(question_dir, "one_stage_response")
                result['output_files'].append({
                    'type': 'one_stage_response',
                    'directory': one_stage_response_dir,
                    'description': '单阶段处理结果'
                })
            else:
                print(f"✗ 第 {batch_index + 1} 个配置（单阶段）处理失败！")
                result['error_message'] = "单阶段处理返回失败状态"
        except Exception as e:
            print(f"✗ 第 {batch_index + 1} 个配置（单阶段）执行异常: {e}")
            result['error_message'] = str(e)

    elif stage_choice == '2':
        # 双阶段模式
        print(f"\n执行双阶段模式，round2图片: {'是' if round2_choice == '1' else '否'}")

        try:
            import test

            # 运行test.py
            success1 = test.run_test(
                model_id=model_id,
                question_type=question_type,
                pinyin_name=pinyin_name,
                images_dir=images_dir_name,
                use_enhance=use_enhance,
                scale=scale,
                use_pixel_connection=use_pixel_connection
            )

            if not success1:
                print(f"✗ 第 {batch_index + 1} 个配置的test.py运行失败")
                result['error_message'] = "第一阶段test.py运行失败"
                return result

            # 保存配置供第二阶段使用
            types_dir = "types"
            question_dir = os.path.join(types_dir, pinyin_name)
            config_data = {
                'model_id': model_id,
                'question_type': question_type,
                'pinyin_name': pinyin_name,
                'images_dir': images_dir_name,
                'question_dir': question_dir,
                'use_enhance': use_enhance,
                'scale': scale,
                'use_pixel_connection': use_pixel_connection
            }
            config_file = save_config(config_data)

            # 添加第一阶段输出文件
            response_dir = os.path.join(question_dir, "response")
            result['output_files'].append({
                'type': 'response',
                'directory': response_dir,
                'description': '第一阶段处理结果'
            })

            if round2_choice == '1':
                # round2发图片：调用test3.py
                success2 = run_script("test3.py", ["--config", config_file])
                if success2:
                    print(f"✓ 第 {batch_index + 1} 个配置（双阶段-发图片）处理完成！")
                    result['success'] = True
                    # 添加第二阶段输出文件
                    round2_response_new_dir = os.path.join(question_dir, "round2_response_new")
                    result['output_files'].append({
                        'type': 'round2_response_new',
                        'directory': round2_response_new_dir,
                        'description': '第二阶段处理结果（发图片）'
                    })
                else:
                    print(f"✗ 第 {batch_index + 1} 个配置的test3.py运行失败")
                    result['error_message'] = "第二阶段test3.py运行失败"
            else:
                # round2不发图片：调用test2.py
                success2 = run_script("test2.py", ["--config", config_file])
                if success2:
                    print(f"✓ 第 {batch_index + 1} 个配置（双阶段-不发图片）处理完成！")
                    result['success'] = True
                    # 添加第二阶段输出文件
                    round2_response_dir = os.path.join(question_dir, "round2_response")
                    result['output_files'].append({
                        'type': 'round2_response',
                        'directory': round2_response_dir,
                        'description': '第二阶段处理结果（不发图片）'
                    })
                else:
                    print(f"✗ 第 {batch_index + 1} 个配置的test2.py运行失败")
                    result['error_message'] = "第二阶段test2.py运行失败"

            # 清理配置文件
            cleanup_config()

        except Exception as e:
            print(f"✗ 第 {batch_index + 1} 个配置（双阶段）执行异常: {e}")
            result['error_message'] = str(e)
            cleanup_config()

    print(f"第 {batch_index + 1} 个批处理配置执行完成\n")
    return result

def main():
    """主函数：控制整个流程"""
    print("=== AI图像识别验证工具 ===")
    print("欢迎使用AI图像识别验证工具！")

    # 第一步：选择输入模式
    input_mode = get_input_mode_choice()

    if input_mode == '2':
        # batch_configs读取模式
        print("\n您选择了batch_configs读取模式")

        # 加载最新的批处理配置文件
        batch_data = get_latest_batch_config()
        if batch_data is None:
            print("无法加载批处理配置，程序退出")
            return

        batch_configs = batch_data.get("batch_configs", [])
        if not batch_configs:
            print("批处理配置文件中没有找到配置项，程序退出")
            return

        print(f"找到 {len(batch_configs)} 个批处理配置，开始依次执行...")

        # 依次执行每个批处理配置，收集结果
        batch_results = []
        for i, config in enumerate(batch_configs):
            result = run_single_batch_config(config, i)
            batch_results.append(result)

        # 输出批处理执行总结
        print_batch_summary(batch_results)
        return

    # 手动输入模式（原有逻辑）
    print("\n您选择了手动输入模式")

    # 第二步：选择单阶段还是双阶段
    stage_choice = get_stage_choice()
    
    if stage_choice == '1':
        # 单阶段：直接调用one_stage_test脚本
        print("\n您选择了单阶段模式")
        success = run_script("one_stage_test.py")
        if success:
            print("\n✓ 单阶段处理完成！")
        else:
            print("\n✗ 单阶段处理失败！")
    
    elif stage_choice == '2':
        # 双阶段：需要进一步选择
        print("\n您选择了双阶段模式")
        
        # 第二步：选择round2是否发图片
        round2_choice = get_round2_image_choice()
        
        if round2_choice == '1':
            # round2发图片：先调用test再调用test3
            print("\n您选择了round2发图片模式")
            print("将依次运行：test.py -> test3.py")

            # 运行test.py并传递双阶段标志
            success1 = run_script("test.py", ["--stage", "dual"])
            if not success1:
                print("\n✗ test.py 运行失败，停止后续流程")
                cleanup_config()
                return

            # 加载test.py保存的配置
            config = load_config()
            if not config:
                print("\n✗ 无法加载配置信息，停止后续流程")
                return

            # 运行test3.py并传递配置
            success3 = run_script("test3.py", ["--config", "temp_config.json"])
            if success3:
                print("\n✓ 双阶段处理完成（round2发图片）！")
            else:
                print("\n✗ test3.py 运行失败！")

            # 清理配置文件
            cleanup_config()

        elif round2_choice == '2':
            # round2不发图片：先调用test再调用test2
            print("\n您选择了round2不发图片模式")
            print("将依次运行：test.py -> test2.py")

            # 运行test.py并传递双阶段标志
            success1 = run_script("test.py", ["--stage", "dual"])
            if not success1:
                print("\n✗ test.py 运行失败，停止后续流程")
                cleanup_config()
                return

            # 加载test.py保存的配置
            config = load_config()
            if not config:
                print("\n✗ 无法加载配置信息，停止后续流程")
                return

            # 运行test2.py并传递配置
            success2 = run_script("test2.py", ["--config", "temp_config.json"])
            if success2:
                print("\n✓ 双阶段处理完成（round2不发图片）！")
            else:
                print("\n✗ test2.py 运行失败！")

            # 清理配置文件
            cleanup_config()
    
    print("\n程序运行结束。")

if __name__ == "__main__":
    main()
